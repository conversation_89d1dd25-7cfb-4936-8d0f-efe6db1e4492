# ============================================================================
# JAEGER TRADING SYSTEM CONFIGURATION
# ============================================================================
# This file contains all configuration parameters for the Jaeger trading system.
# Organized by functional categories for better maintainability.
# ============================================================================

# ============================================================================
# LLM (LARGE LANGUAGE MODEL) CONFIGURATION
# ============================================================================
# Settings for AI model behavior and communication

# Model selection and connection
LM_STUDIO_MODEL_SELECTION_MODE=auto
LM_STUDIO_DEFAULT_MODEL=meta-llama-3.1-8b-instruct

# Model behavior parameters
LLM_TEMPERATURE=0.1
LLM_CONTEXT_LENGTH=32000

# Learning and memory settings
LLM_MAX_LEARNING_SESSIONS=10

# Context length behavior
LLM_AUTO_ADJUST_CONTEXT=true  # Automatically use model's maximum supported context length

# ============================================================================
# BACKTESTING ENGINE CONFIGURATION
# ============================================================================
# Core backtesting parameters for strategy validation

# Account settings
DEFAULT_INITIAL_CASH=100000
DEFAULT_MARGIN=0.01  # 1% margin = 100:1 leverage (correct for CFD trading)
DEFAULT_LEVERAGE=100

# Trading costs and execution (INDEX CFD SPECIFIC)
DEFAULT_SPREAD=0.0001  # 1 pip for index CFDs (backtester handles automatically)
DEFAULT_COMMISSION=0.0
DEFAULT_TRADE_ON_CLOSE=true
DEFAULT_EXCLUSIVE_ORDERS=true
DEFAULT_FINALIZE_TRADES=true
DEFAULT_HEDGING=true

# Risk management defaults
DEFAULT_STOP_LOSS_PCT=2.0
DEFAULT_TAKE_PROFIT_PCT=2.0
DEFAULT_MAX_HOLDING_MINUTES=240
MAX_HOLDING_MINUTES=240

# Position sizing and trade limits (USER CONFIGURABLE)
MAX_POSITION_SIZE_PCT=2.0
MIN_POSITION_SIZE_PCT=0.5
DEFAULT_POSITION_SIZE_PCT=1.0
MAX_CONCURRENT_TRADES=3
MAX_TRADES_PER_DAY=2  # Maximum trades per session (simpler than daily tracking)
POSITION_SIZE_REDUCTION_THRESHOLD=2.0

# Pattern condition thresholds (USER CONFIGURABLE)
RANGE_CONTRACTION_THRESHOLD=0.30
RANGE_EXPANSION_THRESHOLD=2.0
LOW_VOLATILITY_THRESHOLD=0.002
HIGH_VOLATILITY_THRESHOLD=0.01
MEASURED_MOVE_THRESHOLD=1.5
DEFAULT_LOOKBACK_PERIODS=20

# Emergency fallback values (ONLY used if LLM fails to provide values)
FALLBACK_RISK_REWARD_RATIO=2.0
FALLBACK_STOP_LOSS_PERCENTAGE=2.0
FALLBACK_TAKE_PROFIT_PERCENTAGE=6.0

# LLM and validation settings (USER CONFIGURABLE)
LLM_TRANSLATION_TEMPERATURE=0.3
VALIDATOR_MAX_RETRIES=2
VALIDATOR_RETRY_DELAY=1.0
QUALITY_SCORE_EXCELLENT_THRESHOLD=0.85
QUALITY_SCORE_GOOD_THRESHOLD=0.70
QUALITY_SCORE_FAIR_THRESHOLD=0.50

# ============================================================================
# WALK-FORWARD TESTING CONFIGURATION
# ============================================================================
# Parameters for walk-forward validation of trading strategies

WALKFORWARD_DEFAULT_CASH=10000
WALKFORWARD_DEFAULT_COMMISSION=0.0
WALKFORWARD_DEFAULT_MARGIN=1.0
WALKFORWARD_DEFAULT_EXCLUSIVE_ORDERS=true
WALKFORWARD_DEFAULT_N_SPLITS=5
WALKFORWARD_DEFAULT_GAP=0
WALKFORWARD_MIN_MULTIPLIER=1.5

# ============================================================================
# MT4 EXPERT ADVISOR CONFIGURATION
# ============================================================================
# Settings for MetaTrader 4 Expert Advisor generation and execution

# Position sizing and execution
MT4_DEFAULT_LOT_SIZE=0.1
MT4_DEFAULT_SLIPPAGE=2

# Trading time filters
MT4_DEFAULT_START_HOUR=9
MT4_DEFAULT_END_HOUR=16:30
MT4_DEFAULT_USE_TIME_FILTER=false

# ============================================================================
# ORB-FOCUSED SYSTEM CONFIGURATION
# ============================================================================
# Settings for Opening Range Breakout focused analysis (NO behavioral metrics)

# ORB system mode (eliminates 140+ behavioral metrics)
SIMPLIFIED_BEHAVIORAL_INTELLIGENCE=true

# ORB-specific parameters (flexible opening range)
ORB_DEFAULT_PERIOD_MINUTES=15
ORB_DEFAULT_PERIOD_BARS=2
ORB_MIN_PERIOD_BARS=1
ORB_MAX_PERIOD_BARS=6
ORB_MIN_RANGE_SIZE=0.0001
ORB_MAX_RANGE_SIZE=0.01
ORB_SESSION_START_HOUR=8
ORB_SESSION_END_HOUR=16